#!/usr/bin/env python3
"""
Simple test script for Glovas API requests
Run individual tests or all tests at once
"""

from individual_requests import (
    airtime_topup_1_naira,
    data_bundle_25_naira,
    vos_purchase_3_naira,
    vot_purchase_3_naira
)

def test_single_request(request_func, request_name):
    """Test a single request function"""
    print(f"\n{'='*50}")
    print(f"Testing: {request_name}")
    print(f"{'='*50}")

    try:
        response = request_func()
        print(f"✅ Request sent successfully")
        print(f"📊 Status Code: {response.status_code}")
        print(f"📝 Response Headers: {dict(response.headers)}")
        print(f"📄 Response Body (first 500 chars):")
        print(response.text[:500])

        if response.status_code == 200:
            print("✅ Request completed successfully")
        else:
            print(f"⚠️  Request returned status code: {response.status_code}")

    except Exception as e:
        print(f"❌ Error occurred: {e}")
        print(f"Error type: {type(e).__name__}")

def run_all_tests():
    """Run all test requests"""
    print("🚀 Starting Glovas API Tests")
    print("Base URL: http://41.203.65.10:8913/topupservice/service (PRODUCTION)")
    print("Test MSISDN: 2348059999008")
    print("Reseller ID: DIST2348058555399")

    # Test all requests
    test_single_request(airtime_topup_1_naira, "Airtime Topup (1 Naira)")
    test_single_request(data_bundle_25_naira, "Data Bundle (25 Naira)")
    test_single_request(vos_purchase_3_naira, "VOS Purchase (3 Naira)")
    test_single_request(vot_purchase_3_naira, "VOT Purchase (3 Naira)")

    print(f"\n{'='*50}")
    print("🏁 All tests completed!")
    print(f"{'='*50}")

def interactive_menu():
    """Interactive menu for testing individual requests"""
    while True:
        print(f"\n{'='*40}")
        print("Glovas API Test Menu")
        print(f"{'='*40}")
        print("1. Test Airtime Topup (1 Naira)")
        print("2. Test Data Bundle (25 Naira)")
        print("3. Test VOS Purchase (3 Naira)")
        print("4. Test VOT Purchase (3 Naira)")
        print("5. Run All Tests")
        print("6. Exit")
        print(f"{'='*40}")

        choice = input("Enter your choice (1-6): ").strip()

        if choice == '1':
            test_single_request(airtime_topup_1_naira, "Airtime Topup (1 Naira)")
        elif choice == '2':
            test_single_request(data_bundle_25_naira, "Data Bundle (25 Naira)")
        elif choice == '3':
            test_single_request(vos_purchase_3_naira, "VOS Purchase (3 Naira)")
        elif choice == '4':
            test_single_request(vot_purchase_3_naira, "VOT Purchase (3 Naira)")
        elif choice == '5':
            run_all_tests()
        elif choice == '6':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1-6.")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        # Command line argument provided
        arg = sys.argv[1].lower()

        if arg == 'airtime':
            test_single_request(airtime_topup_1_naira, "Airtime Topup (1 Naira)")
        elif arg == 'data':
            test_single_request(data_bundle_25_naira, "Data Bundle (25 Naira)")
        elif arg == 'vos':
            test_single_request(vos_purchase_3_naira, "VOS Purchase (3 Naira)")
        elif arg == 'vot':
            test_single_request(vot_purchase_3_naira, "VOT Purchase (3 Naira)")
        elif arg == 'all':
            run_all_tests()
        else:
            print("❌ Invalid argument. Use: airtime, data, vos, vot, or all")
    else:
        # No arguments, show interactive menu
        interactive_menu()
